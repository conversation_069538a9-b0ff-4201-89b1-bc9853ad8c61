package handlers

import (
	"crypto/rand"
	"encoding/hex"
	"errors"
	"net/url"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/stagedates/shortlink-service/pkg/validators"
	"gofr.dev/pkg/gofr"
)

type DynamicLink struct {
	ID         uuid.UUID `json:"id"`
	SrcURL     string    `json:"shortlink"`
	TargetURL  string    `json:"targetUrl"`
	InsertedAt time.Time `json:"insertedAt"`
	UpdatedAt  time.Time `json:"updatedAt"`
}

func generateRandomPath() string {
	bytes := make([]byte, 3) // 6 character hex string
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

// FirebaseLinkCreationHandler creates a new link in the database
// based on the firebase dynamic link specification found here:
// https://firebase.google.com/docs/dynamic-links/rest
// Target url example "https://stagedates.com/events/star-fm-rooftop-party-nhow-berlin-20250726-vDO1Q"
func FirebaseLinkCreationHandler(app *gofr.App) func(*gofr.Context) (any, error) {
	return func(ctx *gofr.Context) (any, error) {
		var reqLink DynamicLink
		if err := ctx.Bind(&reqLink); err != nil {
			return nil, err
		}

		apiKey, _ := ctx.Value("RequestApiKey").(string)
		if !validators.ApiKeyValidator(app, apiKey) {
			ctx.Logger.Warn("Unauthorized: Invalid or missing API key")
			return nil, errors.New("unauthorized: invalid or missing API key")
		}

		var path string
		if reqLink.SrcURL != "" {
			parsed, err := url.Parse(reqLink.SrcURL)
			if err == nil && parsed.Path != "" && parsed.Path != "/" {
				path = strings.TrimPrefix(parsed.Path, "/")
			}
		}
		if path == "" {
			path = generateRandomPath()
		}

		srcURL := reqLink.SrcURL
		if srcURL == "" || !strings.HasSuffix(srcURL, path) {
			base := reqLink.SrcURL
			if base == "" {
				base = app.Config.GetOrDefault("SHORTLINK_BASE_URL", "https://stagedates.com/")
			}
			srcURL = strings.TrimRight(base, "/") + "/" + path
		}

		now := time.Now()

		var linkID uuid.UUID
		err := ctx.SQL.QueryRowContext(ctx,
			"INSERT INTO shortlink.link (src_url, target_url, inserted_at, updated_at) VALUES ($1, $2, $3, $4) RETURNING id",
			srcURL, reqLink.TargetURL, now, now).Scan(&linkID)
		if err != nil {
			ctx.Logger.Errorf("error inserting link: %v", err)
			return nil, err
		}

		return DynamicLink{
			ID:         linkID,
			SrcURL:     srcURL,
			TargetURL:  reqLink.TargetURL,
			InsertedAt: now,
			UpdatedAt:  now,
		}, nil
	}
}
