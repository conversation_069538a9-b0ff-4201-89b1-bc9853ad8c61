-- Migration script to transfer data from events.tracking_links to shortlink.link
-- Usage: psql -h 127.0.0.1 -p 25432 -U sd-db-user -d backend -f local/migration.sql

\echo 'Starting migration from events.tracking_links to shortlink.link...'

-- Ensure we're connected to the correct database
\c backend

-- Begin transaction for data safety
BEGIN;

-- Display current row counts before migration
\echo 'Row counts before migration:'
SELECT 'events.tracking_links' as table_name, COUNT(*) as row_count FROM events.tracking_links
UNION ALL
SELECT 'shortlink.link' as table_name, COUNT(*) as row_count FROM shortlink.link;

-- Insert data from tracking_links to link table
-- Mapping: url -> src_url, other columns remain the same
INSERT INTO shortlink.link (target_url, src_url, inserted_at, updated_at, deleted_at)
SELECT 
    target_url,
    url as src_url,  -- mapping url column to src_url
    inserted_at,
    updated_at,
    deleted_at
FROM events.tracking_links
ON CONFLICT DO NOTHING;  -- <PERSON>p duplicates if any unique constraints exist

-- Display row counts after migration
\echo 'Row counts after migration:'
SELECT 'events.tracking_links' as table_name, COUNT(*) as row_count FROM events.tracking_links
UNION ALL
SELECT 'shortlink.link' as table_name, COUNT(*) as row_count FROM shortlink.link;

-- Display number of rows actually migrated
\echo 'Migration summary:'
SELECT COUNT(*) as rows_migrated FROM events.tracking_links;

COMMIT;

\echo 'Migration completed successfully!'
